package services

import (
	"context"
	"encoding/base64"
	webrtc "smart-city/internal/app/camera/delivery/webRTC"
	"smart-city/internal/app/camera/dto"
	camRepositories "smart-city/internal/app/camera/repository"
	premiseRepositories "smart-city/internal/app/premise/repository"
	"smart-city/internal/models"

	"github.com/google/uuid"
)

type Service struct {
	cameraRepo  camRepositories.CameraRepository
	premiseRepo premiseRepositories.PremiseRepository
}

func NewCameraService(cameraRepo camRepositories.CameraRepository, premiseRepo premiseRepositories.PremiseRepository) *Service {
	return &Service{cameraRepo: cameraRepo, premiseRepo: premiseRepo}
}

func (s *Service) CreateCamera(ctx context.Context, createCameraDto *dto.CreateCameraDto) (*models.Camera, error) {
	Camera := &models.Camera{
		Name:      createCameraDto.Name,
		Location:  createCameraDto.Location,
		StreamURL: createCameraDto.StreamURL,
		IsActive:  false,
	}
	if createCameraDto.PremiseID != "" {
		premiseID, err := uuid.Parse(createCameraDto.PremiseID)

		if err != nil {
			return nil, err
		}

		premise, err := s.premiseRepo.GetPremiseByID(ctx, premiseID.String())

		if err != nil {
			return nil, err
		}
		Camera.Premise = premise
		Camera.PremiseID = premiseID

	}

	return s.cameraRepo.CreateCamera(ctx, Camera)
}

func (s *Service) GetCameras(ctx context.Context) ([]models.Camera, error) {
	return s.cameraRepo.GetCameras(ctx)
}

func (s *Service) StartPublishing(ctx context.Context, req *dto.SdpRequest) (*dto.SdpResponse, error) {
	broadcaster := webrtc.NewBroadcaster(req.CameraID)
	//mockup sdp
	mockSDP := `v=0
				o=- 46117327 2 IN IP4 127.0.0.1
				s=-
				t=0 0
				a=group:BUNDLE 0
				a=msid-semantic:WMS
				m=audio 9 UDP/TLS/RTP/SAVPF 111
				c=IN IP4 0.0.0.0
				a=rtpmap:111 opus/48000/2
				`

	encoded := base64.StdEncoding.EncodeToString([]byte(mockSDP))

	decoded, err := webrtc.DecodeSDP(encoded)
	if err != nil {
		return nil, err
	}
	answer, err := broadcaster.StartBroadcast(decoded)
	if err != nil {
		return nil, err
	}

	return &dto.SdpResponse{SDP: *answer}, nil

}
