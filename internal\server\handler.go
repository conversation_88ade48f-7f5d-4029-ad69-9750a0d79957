package server

import (
	"net/http"
	usersHttp "smart-city/internal/app/user/delivery/http"
	usersRepository "smart-city/internal/app/user/repository"
	usersService "smart-city/internal/app/user/service"
	middleware "smart-city/internal/middlewares"

	"github.com/labstack/echo/v4"
)

func (s *Server) MapHandlers(e *echo.Echo) error {
	// Init repositories
	usersRepo := usersRepository.NewUserRepository(s.db)
	// Init service
	usersSvc := usersService.NewUserService(*usersRepo)
	// Init handlers
	usersHandlers := usersHttp.NewHandler(*usersSvc)

	mw := middleware.NewMiddlewareManager(s.cfg, []string{"*"}, s.logger)
	e.Use(mw.RequestLoggerMiddleware)
	v1 := e.Group("/api/v1")

	health := v1.Group("/health")
	usersGroup := v1.Group("/users")

	health.GET("", func(c echo.Context) error {
		return c.<PERSON>SO<PERSON>(http.StatusOK, map[string]string{"status": "OK"})
	})
	usersHandlers.RegisterRoutes(usersGroup)
	return nil

}
