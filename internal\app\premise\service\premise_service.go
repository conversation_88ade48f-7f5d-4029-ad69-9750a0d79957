package services

import (
	"context"
	"smart-city/internal/app/premise/dto"
	repositories "smart-city/internal/app/premise/repository"
	"smart-city/internal/models"
)

type Service struct {
	premiseRepo repositories.PremiseRepository
}

func NewpremiseService(premiseRepo repositories.PremiseRepository) *Service {
	return &Service{premiseRepo: premiseRepo}
}

func (s *Service) Createpremise(ctx context.Context, createPremiseDto *dto.CreatePremiseDto) (*models.Premise, error) {
	premise := &models.Premise{
		Name:            createPremiseDto.Name,
		Location:        createPremiseDto.Location,
	}
	if createPremiseDto.ParentPremiseID != "" {
		premise.ParentPremiseID = &createPremiseDto.ParentPremiseID
	}

	return s.premiseRepo.CreatePremise(ctx, premise)
}

func (s *Service) GetPremises(ctx context.Context) ([]models.Premise, error) {
	return s.premiseRepo.GetPremises(ctx)
}
